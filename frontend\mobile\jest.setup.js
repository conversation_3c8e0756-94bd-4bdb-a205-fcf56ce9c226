// Include any global setup for the Jest testing environment
require('@testing-library/jest-native/extend-expect');

// Mock the Expo modules that might cause issues in the testing environment
jest.mock('expo-font', () => ({}));
jest.mock('expo-asset', () => ({}));

// Mock expo-linear-gradient with a proper React component
jest.mock('expo-linear-gradient', () => {
  const React = require('react');
  const { View } = require('react-native');

  const LinearGradient = React.forwardRef((props, ref) => {
    return React.createElement(View, { ...props, ref });
  });

  return { LinearGradient };
});

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
  Link: 'TouchableOpacity',
}));

// Mock the Animated module instead of NativeAnimatedHelper
jest.mock('react-native/Libraries/Animated/Animated', () => ({
  timing: () => ({
    start: jest.fn(),
  }),
  loop: jest.fn(),
  Value: jest.fn(() => ({
    interpolate: jest.fn(),
  })),
}));

// Mock Alert separately to avoid issues with React Native modules
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock React Native components that might cause issues
jest.mock('react-native-vector-icons', () => 'Icon');

// Mock custom components with proper React components
jest.mock('./src/components/common/CustomTextInput', () => {
  const React = require('react');
  const { TextInput } = require('react-native');

  const CustomTextInput = React.forwardRef((props, ref) => {
    return React.createElement(TextInput, { ...props, ref });
  });

  return { CustomTextInput };
});

jest.mock('./src/components/common/CustomButton', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');

  const CustomButton = React.forwardRef(({ title, children, ...props }, ref) => {
    return React.createElement(
      TouchableOpacity,
      { ...props, ref },
      React.createElement(Text, {}, title || children)
    );
  });

  return { CustomButton };
});

jest.mock('./src/components/common/PhoneNumberInput', () => {
  const React = require('react');
  const { TextInput } = require('react-native');

  const PhoneNumberInput = React.forwardRef((props, ref) => {
    return React.createElement(TextInput, { ...props, ref });
  });

  return { PhoneNumberInput };
});

jest.mock('./src/components/common/CheckboxInput', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');

  const CheckboxInput = React.forwardRef(({ label, ...props }, ref) => {
    return React.createElement(
      TouchableOpacity,
      { ...props, ref },
      React.createElement(Text, {}, label)
    );
  });

  return { CheckboxInput };
});

jest.mock('./src/components/auth/PasswordStrengthIndicator', () => {
  const React = require('react');
  const { View, Text } = require('react-native');

  const PasswordStrengthIndicator = React.forwardRef(({ strength, ...props }, ref) => {
    return React.createElement(
      View,
      { ...props, ref },
      React.createElement(Text, {}, strength || 'Strong')
    );
  });

  return { PasswordStrengthIndicator };
});

jest.mock('./src/components/auth/RoleCard', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');

  const RoleCard = React.forwardRef(({ role, ...props }, ref) => {
    return React.createElement(
      TouchableOpacity,
      { ...props, ref },
      React.createElement(Text, {}, role?.title || 'Role')
    );
  });

  return { RoleCard };
});

jest.mock('./src/components/auth/OTPInput', () => {
  const React = require('react');
  const { View, TextInput } = require('react-native');

  const OTPInput = React.forwardRef((props, ref) => {
    return React.createElement(
      View,
      { ref },
      Array.from({ length: 6 }, (_, i) =>
        React.createElement(TextInput, { key: i, defaultValue: '', ...props })
      )
    );
  });

  return { OTPInput };
});

jest.mock('./src/components/profile/ProfileImagePicker', () => {
  const React = require('react');
  const { TouchableOpacity, Text } = require('react-native');

  const ProfileImagePicker = React.forwardRef((props, ref) => {
    return React.createElement(
      TouchableOpacity,
      { ...props, ref },
      React.createElement(Text, {}, 'Profile Image Picker')
    );
  });

  return { ProfileImagePicker };
});

jest.mock('./src/components/profile/SkillsSelector', () => {
  const React = require('react');
  const { View, Text } = require('react-native');

  const SkillsSelector = React.forwardRef((props, ref) => {
    return React.createElement(
      View,
      { ...props, ref },
      React.createElement(Text, {}, 'Skills Selector')
    );
  });

  return { SkillsSelector };
});

jest.mock('./src/components/profile/LocationSelector', () => {
  const React = require('react');
  const { View, Text } = require('react-native');

  const LocationSelector = React.forwardRef((props, ref) => {
    return React.createElement(
      View,
      { ...props, ref },
      React.createElement(Text, {}, 'Location Selector')
    );
  });

  return { LocationSelector };
});

// Mock constants
jest.mock('./src/constants/Colors', () => ({
  Colors: {
    light: {
      primary: '#007AFF',
      secondary: '#5856D6',
      background: '#FFFFFF',
      text: '#000000',
    },
  },
}));

// This enables proper error messages for React tests
globalThis.ErrorUtils = {
  setGlobalHandler: jest.fn(),
};
