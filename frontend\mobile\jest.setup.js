// Include any global setup for the Jest testing environment
require('@testing-library/jest-native/extend-expect');

// Fix for React Native StatusBar clearImmediate issue in jsdom
global.setImmediate = global.setImmediate || ((fn, ...args) => global.setTimeout(fn, 0, ...args));
global.clearImmediate = global.clearImmediate || global.clearTimeout;

// Mock the Expo modules that might cause issues in the testing environment
jest.mock('expo-font');
jest.mock('expo-asset');
jest.mock('expo-linear-gradient');

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
}));

// Mock React Native components that might cause issues
jest.mock('react-native-vector-icons');

// Mock StatusBar to prevent clearImmediate issues
jest.mock('react-native/Libraries/Components/StatusBar/StatusBar', () => {
  const React = require('react');
  const { View } = require('react-native');

  const StatusBar = React.forwardRef((props, ref) => {
    return React.createElement(View, { ...props, ref });
  });

  StatusBar.setBarStyle = jest.fn();
  StatusBar.setBackgroundColor = jest.fn();
  StatusBar.setHidden = jest.fn();
  StatusBar.setTranslucent = jest.fn();
  StatusBar.setNetworkActivityIndicatorVisible = jest.fn();
  StatusBar.pushStackEntry = jest.fn();
  StatusBar.popStackEntry = jest.fn();
  StatusBar.replaceStackEntry = jest.fn();

  return StatusBar;
});

// Mock constants
jest.mock('./src/constants/Colors', () => ({
  Colors: {
    light: {
      primary: '#4A6FFF',
      secondary: '#63E2FF',
      background: '#F8FAFF',
      text: '#333333',
      accent: '#FF6B9A',
      success: '#4CD964',
      warning: '#FFCC00',
      danger: '#FF3B30',
    },
  },
}));

// This enables proper error messages for React tests
globalThis.ErrorUtils = {
  setGlobalHandler: jest.fn(),
};
