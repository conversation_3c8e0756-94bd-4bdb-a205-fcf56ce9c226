import React from 'react';
import { render } from '@testing-library/react-native';

// Now import the component - mocks are handled in jest.setup.js
import WelcomeScreen from '../WelcomeScreen';

describe('WelcomeScreen - Simple Tests', () => {
  it('renders basic text content', () => {
    const { getByText } = render(<WelcomeScreen />);

    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
  });

  it('displays the app icon', () => {
    const { getByText } = render(<WelcomeScreen />);

    expect(getByText('🤝')).toBeTruthy();
  });
});
