import React from 'react';
import { render } from '@testing-library/react-native';

// Mock all problematic modules before importing the component
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

jest.mock('expo-linear-gradient', () => {
  const React = require('react');
  const { View } = require('react-native');
  
  const LinearGradient = React.forwardRef((props, ref) => {
    return React.createElement(View, { ...props, ref });
  });
  
  return { LinearGradient };
});

jest.mock('../../../constants/Colors', () => ({
  Colors: {
    light: {
      primary: '#007AFF',
      secondary: '#5856D6',
    },
  },
}));

// Now import the component after mocking
import WelcomeScreen from '../WelcomeScreen';

describe('WelcomeScreen - Simple Tests', () => {
  it('renders basic text content', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
  });

  it('displays the app icon', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('🤝')).toBeTruthy();
  });
});
