import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import WelcomeScreen from '../WelcomeScreen';

// Mock expo-router for this specific test
const mockPush = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

describe('WelcomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<WelcomeScreen />);

    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
    expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
    expect(getByText('Get Started')).toBeTruthy();
    // The text is nested, so we need to search for the combined text
    expect(getByText(/Already have an account\?.*Sign In/)).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('displays the app logo and icon', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('🤝')).toBeTruthy();
  });

  it('handles get started button press', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    const getStartedButton = getByText('Get Started');
    fireEvent.press(getStartedButton);
    
    expect(mockPush).toHaveBeenCalledWith('/auth/register');
  });

  it('displays sign in text but does not handle press (commented out)', () => {
    const { getByText } = render(<WelcomeScreen />);

    const signInText = getByText(/Already have an account\?.*Sign In/);
    expect(signInText).toBeTruthy();

    // The sign in functionality is commented out in the component
    // so we just verify the text is displayed
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('has proper styling structure', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    // Verify main content elements are present
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('🤝')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
    expect(getByText('Get Started')).toBeTruthy();
  });

  it('displays the correct subtitle text', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
  });

  it('has accessible button elements', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    const getStartedButton = getByText('Get Started');
    expect(getStartedButton).toBeTruthy();
    
    // Verify button is pressable
    fireEvent.press(getStartedButton);
    expect(mockPush).toHaveBeenCalled();
  });

  it('renders with proper component hierarchy', () => {
    const { getByText } = render(<WelcomeScreen />);

    // Verify all main sections are rendered
    const appTitle = getByText('HireNow');
    const icon = getByText('🤝');
    const title = getByText('Find Flexible Work, Anytime');
    const subtitle = getByText('Connect with local opportunities that fit your schedule and skills');
    const button = getByText('Get Started');
    const signInText = getByText(/Already have an account\?.*Sign In/);

    expect(appTitle).toBeTruthy();
    expect(icon).toBeTruthy();
    expect(title).toBeTruthy();
    expect(subtitle).toBeTruthy();
    expect(button).toBeTruthy();
    expect(signInText).toBeTruthy();
  });
});
