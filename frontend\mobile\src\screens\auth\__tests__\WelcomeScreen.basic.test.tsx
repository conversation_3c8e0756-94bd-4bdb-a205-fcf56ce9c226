import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';

const mockPush = jest.fn();

// Mock the problematic modules individually
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));
jest.mock('expo-linear-gradient');
jest.mock('../../../constants/Colors');

// Import the component after mocking
import WelcomeScreen from '../WelcomeScreen';

describe('WelcomeScreen - Basic Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the app title', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText('HireNow')).toBeTruthy();
  });

  it('renders the main title', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
  });

  it('renders the subtitle', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
  });

  it('renders the app icon', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText('🤝')).toBeTruthy();
  });

  it('renders the get started button', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText('Get Started')).toBeTruthy();
  });

  it('renders the sign in text', () => {
    const { getByText } = render(<WelcomeScreen />);
    expect(getByText(/Already have an account\?.*Sign In/)).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('handles get started button press', () => {
    const { getByText } = render(<WelcomeScreen />);
    const getStartedButton = getByText('Get Started');
    
    fireEvent.press(getStartedButton);
    
    expect(mockPush).toHaveBeenCalledWith('/auth/register');
  });
});
